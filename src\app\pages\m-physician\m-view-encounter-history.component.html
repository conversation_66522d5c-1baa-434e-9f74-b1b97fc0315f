<div class="patient-cards">
    <div class="patient-card flex flex-col">
        <div class="text-center bg-gray-200 p-2">
            CPT Codes
        </div>
        <div class="flex flex-col mobile-p-3 cursor-pointer">
            <ng-container *ngFor="let thData of listOfHistory.cptEditHistory">
            <div class="flex">
                <span class="font-medium">{{thData.role}} {{thData.createddate|date: 'MM/dd/yyyy'}}</span>
            </div>
            <div class="my-2 h-1 w-12 border-t-2"></div>
            <div class="flex flex-col border-b pb-2">
                    <div *ngFor="let cptData of thData?.cptcodes"
                        tooltip="{{cptData.deletedReson}}" [style.color]="cptData.color">
                        {{cptData.code}}
                    </div>
            </div>
            </ng-container>
        </div>
    </div>
    <div class="patient-card flex flex-col">
        <div class="text-center bg-gray-200 p-2">
            ICD Codes
        </div>
        <div class="flex flex-col mobile-p-3 cursor-pointer">
            <ng-container *ngFor="let thData of listOfHistory.icdEditHistory">
            <div class="flex">
                <span class="font-medium">{{thData.role}} {{thData.createddate|date: 'MM/dd/yyyy'}}</span>
            </div>
            <div class="my-2 h-1 w-12 border-t-2"></div>
            <div class="flex flex-col border-b pb-2">
                    <div *ngFor="let icdData of thData?.icdcodes"
                        [style.color]="icdData.color">
                        {{icdData.code}}
                </div>
            </div>
            </ng-container>
        </div>
    </div>

</div>