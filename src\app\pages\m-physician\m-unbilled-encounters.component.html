<!-- Mobile Unbilled Encounters Component -->
<div class="container-fluid p-2">
    <!-- Header Card -->
    <div class="card shadow-sm border-0 mb-3">
        <div class="card-header bg-primary text-white p-2">
            <h6 class="mb-0 font-weight-bold">
                <i class="fas fa-clipboard-list mr-2"></i>
                Unbilled Encounters
            </h6>
        </div>

        <!-- Filter Section -->
        <div class="card-body p-2" [formGroup]="FilterForm">
            <!-- Facility Selection -->
            <div class="form-group mb-2">
                <label class="small font-weight-bold text-secondary mb-1">Select Facility</label>
                <select id="ddlFacility"
                        class="form-control form-control-sm"
                        formControlName="ddlFacility"
                        (change)="getUnBilledEncounters()"
                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <option value="">---Select Facility---</option>
                    <option value="All">All Facilities</option>
                    <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                        {{s.facilityName}}
                    </option>
                </select>
                <div *ngIf="submitted && f['ddlFacility'].errors" class="invalid-feedback">
                    <div *ngIf="f['ddlFacility'].errors['required']">Facility is required</div>
                </div>
            </div>

            <!-- Search Input -->
            <div class="form-group mb-2">
                <label class="small font-weight-bold text-secondary mb-1">Search</label>
                <div class="input-group input-group-sm">
                    <input type="text"
                           class="form-control"
                           maxlength="1000"
                           (keyup)="search(searchKey)"
                           placeholder="Search for Name/Account#"
                           [(ngModel)]="searchKey"
                           [ngModelOptions]="{standalone: true}">
                    <div class="input-group-append">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Sort Controls -->
            <div class="row">
                <div class="col-6">
                    <button type="button"
                            class="btn btn-outline-primary btn-sm btn-block"
                            (click)="toggleSideNav('sort')"
                            [class.active]="hideSideNav && toggleType == 'sort'">
                        <i class="fas fa-sort mr-1"></i> Sort
                    </button>
                </div>
                <div class="col-6">
                    <div class="text-right small text-muted">
                        {{listOfPatients?.length || 0}} encounters
                    </div>
                </div>
            </div>

            <!-- Sort Panel -->
            <div *ngIf="hideSideNav && toggleType == 'sort'"
                 class="mt-3 p-2 bg-light rounded border">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0 font-weight-bold text-secondary">Sort Options</h6>
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary"
                            (click)="toggleSideNav('close')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="form-group mb-2">
                    <label class="small font-weight-bold text-secondary mb-1">Sort By</label>
                    <select class="form-control form-control-sm"
                            (change)="onChangeForSort($any($event.target).value)">
                        <option *ngFor="let sortOption of sortOptions"
                                [value]="sortOption.id"
                                [selected]="sortOption.id == sortColumnBy">
                            {{sortOption.name}}
                        </option>
                    </select>
                </div>

                <div class="form-group mb-0">
                    <label class="small font-weight-bold text-secondary mb-1">Sort Order</label>
                    <select class="form-control form-control-sm"
                            (change)="onChangeForSortOrder($any($event.target).value)">
                        <option *ngFor="let sortOrder of sortOrders"
                                [value]="sortOrder"
                                [selected]="sortOrder == orderBy">
                            {{sortOrder === 'asc' ? 'Ascending' : 'Descending'}}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="row">
        <div class="col-12">
            <!-- Loading State -->
            <div *ngIf="!listOfPatients && submitted" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading encounters...</p>
            </div>

            <!-- Empty State -->
            <div *ngIf="listOfPatients && listOfPatients.length === 0"
                 class="text-center py-5">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Unbilled Encounters Found</h5>
                <p class="text-muted">Try adjusting your search criteria or select a different facility.</p>
            </div>

            <!-- Patient Cards -->
            <div *ngIf="listOfPatients && listOfPatients.length > 0">
                <ng-container *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 10, currentPage: p }; let i = index;">
                    <div *ngIf="item.account_Number!=''"
                         class="card mb-3 shadow-sm border-left-primary">

                        <!-- Card Header -->
                        <div class="card-header bg-light p-2 cursor-pointer"
                             [popover]="popTemplate"
                             popoverTitle="{{item.patient_Name}}"
                             placement="bottom"
                             triggers="click"
                             [outsideClick]="true">

                            <div class="d-flex justify-content-between align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 font-weight-bold text-primary"
                                        [ngClass]="{
                                            'text-success': (item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'),
                                            'text-warning': (item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null),
                                            'text-info': (item.hasPreviousEncounter=='1' && item.encounteR_ID==null && item.resetPriorEncounterStatus==null),
                                            'text-secondary': (item.hasPreviousEncounter=='0')
                                        }">
                                        {{item.patient_Name}}
                                    </h6>
                                    <div class="small text-muted">
                                        <i class="fas fa-id-card mr-1"></i>
                                        Account: {{item.account_Number}}
                                    </div>
                                </div>
                                <div class="text-right">
                                    <button class="btn btn-sm btn-outline-danger"
                                            title="Remove Encounter"
                                            (click)="openDeleteConfirmPopup(item)">
                                        <i class="far fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Card Body -->
                        <div class="card-body p-2">
                            <div class="row">
                                <div class="col-12 mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-hospital mr-2 text-primary"></i>
                                        <span class="badge badge-primary">{{item.facility_Name}}</span>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="small text-muted mb-1">
                                        <i class="fas fa-calendar-alt mr-1"></i>
                                        Encounter Seen Date
                                    </div>
                                    <div class="font-weight-bold small">
                                        {{item.encounterseendate}}
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="small text-muted mb-1">
                                        <i class="fas fa-sign-in-alt mr-1"></i>
                                        Admission Date
                                    </div>
                                    <div class="font-weight-bold small">
                                        {{item.admit_Datetime | date: 'MM/dd/yyyy hh:mm a'}}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Popover Template -->
                        <ng-template #popTemplate>
                            <div class="p-2">
                                <div class="text-center">
                                    <button class="btn btn-primary btn-sm btn-block"
                                            [routerLink]="['/m-physician/start-new-encounter']"
                                            [state]="{
                                                patient: item,
                                                encounterSeenDate: item.encounterseendate,
                                                backUrl: '/m-physician/unbilled-encounters',
                                                facilityType: item.isPrimeFacility,
                                                filterObj: {
                                                    p: p,
                                                    searchByName: searchKey,
                                                    ddlFacility: FilterForm.value.ddlFacility
                                                }
                                            }">
                                        <i class="fas fa-plus mr-1"></i>
                                        <span *ngIf="item.encounterid == 0">Start New Encounter</span>
                                        <span *ngIf="item.encounterid != 0">My Encounter</span>
                                    </button>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="listOfPatients && listOfPatients.length > 0" class="row mt-3">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body p-2 text-center">
                    <pagination-controls
                        previousLabel="Previous"
                        nextLabel="Next"
                        (pageChange)="p = $event"
                        class="pagination-sm">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Footer -->
    <div *ngIf="listOfPatients && listOfPatients.length > 0" class="row mt-2">
        <div class="col-12">
            <div class="text-center small text-muted">
                Showing {{((p-1) * 10) + 1}} to {{Math.min(p * 10, listOfPatients.length)}} of {{listOfPatients.length}} encounters
            </div>
        </div>
    </div>
</div>

<!-- Custom Styles for Mobile Optimization -->
<style>
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.cursor-pointer {
    cursor: pointer;
}

.card-header.bg-light {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6;
}

.badge-primary {
    background-color: #007bff;
    font-size: 0.75em;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Mobile responsive adjustments */
@media (max-width: 576px) {
    .container-fluid {
        padding: 0.5rem;
    }

    .card-body {
        padding: 0.75rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .small {
        font-size: 0.8rem;
    }
}

/* Encounter status color coding */
.text-success {
    color: #28a745 !important; /* Today's encounter */
}

.text-warning {
    color: #ffc107 !important; /* Draft encounter */
}

.text-info {
    color: #17a2b8 !important; /* Prior encounter */
}

.text-secondary {
    color: #6c757d !important; /* No encounter */
}

/* Loading spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Pagination styling */
::ng-deep .ngx-pagination {
    margin: 0;
}

::ng-deep .ngx-pagination .current {
    background-color: #007bff;
    border-color: #007bff;
}

::ng-deep .ngx-pagination a:hover {
    background-color: #e9ecef;
}
</style>
