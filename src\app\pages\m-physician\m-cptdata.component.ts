import { Component, Input } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { MStartNewEncounterComponent } from './m-start-new-encounter.component';
import { MatTabChangeEvent } from '@angular/material/tabs';

@Component({
  selector: 'app-m-cptdata',
  templateUrl: './m-cptdata.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MCPTDataComponent {

  @Input() lisfOfCPTData: Array<any> = [];
  public chkValuesArray: Array<any> = [];
  public editedValue: string;
  public searchCPTs: string;
  public request: any = {};
  public lisfOfSearchCPTData: Array<any> = [];
  public filterCPTs: string; v
  public isSearch: boolean = false;
  public timeout: any = null;
  constructor(private readonly encrDecr: EncrDecrServiceService, private readonly commonServ: CommonService, private readonly startnewEnvCmp: MStartNewEncounterComponent) { }

  search() {
    this.lisfOfSearchCPTData = [];
    this.filterCPTs = "";
    this.isSearch = true;
  }

  fav() {
    this.isSearch = false;
    this.startnewEnvCmp.getCPTData();
  }

  searchCPTData() {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      this.commonServ.startLoading();
      this.request.CPTNAME = this.encrDecr.set(this.filterCPTs);
      this.request.PHYSICIAN_MAIL_ID = this.encrDecr.set('PHYSICIAN');
      this.commonServ.searchCPTData(this.request).subscribe((p: any) => {
        this.lisfOfSearchCPTData = p;
        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
      });
    }, 2000);
  }

  chkChangeEvent(event) {
    for (let item of this.lisfOfCPTData) {
      if (item.cpT_ID == event.target.id) {
        item.checked = event.target.checked;
      }
    }
  }

  chkChangeEventSearch(event) {
    for (let item of this.lisfOfSearchCPTData) {
      if ('sch-' + item.cpT_ID == event.target.id) {
        item.checked = event.target.checked;
      }
    }
  }

  addCPTData(listOfCpts) {
    this.commonServ.startLoading();
    listOfCpts.forEach(x => {
      if (this.startnewEnvCmp.testData.listofTestCPTS == null && x.checked) {
        this.startnewEnvCmp.testData.listofTestCPTS = [];
        this.startnewEnvCmp.testData.listofTestCPTS.push(x.cptname);
      }
      else if (this.startnewEnvCmp.testData.listofTestCPTS && x.checked) {
        this.startnewEnvCmp.testData.listofTestCPTS.push(x.cptname);
      }
    });
    this.commonServ.stopLoading();
  }

  addCPTDataSearch(lisfOfSearchCPTData) {
    this.commonServ.startLoading();
    lisfOfSearchCPTData.forEach(x => {
      if (this.startnewEnvCmp.testData.listofTestCPTS == null && x.checked) {
        this.startnewEnvCmp.testData.listofTestCPTS = [];
        this.startnewEnvCmp.testData.listofTestCPTS.push(x.cptname);
      }
      else if (this.startnewEnvCmp.testData.listofTestCPTS && x.checked) {
        this.startnewEnvCmp.testData.listofTestCPTS.push(x.cptname);
      }
    });
    this.commonServ.stopLoading();
  }

  favUnfavCPTCodesAdd(status, item) {
    let confirmMessage = 'Do you want to ' + (status == 0 ? 'unfavorite' : 'favorite') + ' this CPT Code?';
    if (confirm(confirmMessage)) {
      this.commonServ.startLoading();
      this.request.PHYSICIANMAILID = this.encrDecr.set('PHYSICIAN');
      this.request.STATUS = status;
      this.request.CPT_ID = item.cpT_ID;
      this.commonServ.insertCPTData(this.request).subscribe((p: any) => {
        this.request = {};
        if (p > 0) {
          item.status = status;
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  onMatGroupTabClick(event: MatTabChangeEvent): void {
    if (event.tab.textLabel == 'Favorites') {
      this.fav();
    }
    else{
      this.search()
    }
  }

}

