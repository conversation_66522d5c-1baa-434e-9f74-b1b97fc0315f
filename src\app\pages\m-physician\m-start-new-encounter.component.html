<div class="details-header">
    <button class="back-btn" (click)="back()">
        <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
    </button>
    <div class="patient-name ">{{patient.patient_Name}} {{patient.sex}} ({{patient.sex}}Y)</div>
</div>

<div class="w-full bg-white">
    <mat-tab-group (selectedTabChange)="onMatGroupTabClick($event)">
        <mat-tab label="Details">
            <ng-template matTabContent>
                <div class="w-full mobile-p-4">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <td class="text-secondary2">Account</td>
                                <td><span class="font-medium">{{patient.account_Number || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Room</td>
                                <td><span class="font-medium">{{patient.room_Number || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Type</td>
                                <td><span class="font-medium">{{patient.admission_Type || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">DOB</td>
                                <td><span class="font-medium">{{patient.dbo || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">LOS</td>
                                <td><span class="font-medium">{{patient.arithmetic_Mean_LOS || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Attending</td>
                                <td><span class="font-medium">{{patient.attending_Physician_InApp || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Payor</td>
                                <td><span class="font-medium">{{patient.reimbursement_Type || '-'}}</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="w-full mt-4">
                        <button class="mark-seen-btn" (click)="viewSubmitEncounterPop()">Mark as seen</button>
                        <div class="details-buttons mt-2">
                            <button class="details-btn" (click)="viewPatientHistory()">
                                <img src="assets/icons/icon-history.svg" class="img-icon">
                            </button>
                            <button class="details-btn" (click)="viewNotes()">
                                <img src="assets/icons/icon-note.svg" class="img-icon">
                            </button>
                            <button class="details-btn" (click)="viewAttachments()">
                                <img src="assets/icons/icon-attachment.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                </div>
                <div class="code-content">
                    <!-- Subgroups starts -->
                    <div class="patient-card flex flex-col mobile-p-3"
                        *ngIf="testData.listofSubGroup?.length>0 && userAccess.residentAccess=='NO'">
                        <div class="flex flex-col justify-between mb-2">
                            <div class="px-2">
                                <div class="text-md font-medium">
                                    Subgroups
                                </div>
                                <div class="text-secondary text-sm">
                                    Defaults to {{testData.group_name}} group if no subgroup is selected
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col mt-3">
                            <div class="filter-tabs">
                                <div class="facility-dropdown mr-0">
                                    <mat-select id="subgroup" class="form-control" [(ngModel)]="sub_group_name"
                                        [ngModelOptions]="{standalone: true}">
                                        <mat-option [value]="''" disabled>---Select Subgroup---</mat-option>
                                        <mat-option [value]="s.subGroupName" *ngFor="let s of testData.listofSubGroup">
                                            {{s.subGroupName}}
                                        </mat-option>
                                    </mat-select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Subgroups ends -->
                    <!-- CPT Starts -->
                    <div class="patient-card flex flex-col mobile-p-3" *ngIf="testData?.listofTestCPTS?.length>0; else noCPT">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium px-2">CPT/HCPCS Codes</span>
                            <button class="close-btn" (click)="viewCPTCodes()">
                                <img src="assets/icons/icon-pluscircle-solid-s.svg" class="img-icon">
                            </button>
                        </div>
                        <div class="flex flex-col">
                            <div class="p-2 border-b" *ngFor="let cpt of testData.listofTestCPTS">
                                <div class="flex items-center">
                                    <button class="close-btn" (keyup)="deleteCPTCode(testData.listofTestCPTS,cpt)"
                                        (click)="deleteCPTCode(testData.listofTestCPTS,cpt)">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>{{cpt}}</div>
                                        <div class="text-link text-sm" (click)="getModifierData(cpt)">Add/Edit modifiers
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ng-template #noCPT>
                        <div class="code-section">
                            <div class="code-item" (click)="viewCPTCodes()">
                                <div class="code-icon cpt-icon">
                                    <img src="assets/icons/icon-PlusCircle-s.svg" class="img-icon">
                                </div>
                                <div class="code-desc">
                                    <span class="font-medium">Add new CPT codes</span>
                                    <span class="chevron">
                                        <img src="assets/icons/icon-caret-Right.svg" class="img-icon">
                                    </span>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                    <!-- CPT Ends -->
                    <!-- ICD Starts -->
                    <div class="patient-card flex flex-col mobile-p-3" *ngIf="testData?.listofTestICDS?.length>0; else noICD">
                        <div class="flex justify-between mb-2">
                            <span class="font-medium px-2">ICD Codes</span>
                            <button class="close-btn" (click)="viewICDCodes()">
                                <img src="assets/icons/icon-pluscircle-solid-b.svg" class="img-icon">
                            </button>
                        </div>
                        <div class="flex flex-col">
                            <div class="p-2 border-b" *ngFor="let icd of testData.listofTestICDS">
                                <div class="flex items-center">
                                    <button class="close-btn" (keyup)="deleteICDCode(testData.listofTestICDS,icd)"
                                        (click)="deleteICDCode(testData.listofTestICDS,icd)">
                                        <img src="assets/icons/icon-trash.svg" class="img-icon">
                                    </button>
                                    <div class="flex flex-col ml-2">
                                        <div>{{icd}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ng-template #noICD>
                        <div class="code-section">
                            <div class="code-item" (click)="viewICDCodes()">
                                <div class="code-icon icd-icon">
                                    <img src="assets/icons/icon-PlusCircle-b.svg" class="img-icon">
                                </div>
                                <div class="code-desc">
                                    <span class="font-medium">Add new ICD codes</span>
                                    <span class="chevron">
                                        <img src="assets/icons/icon-caret-Right.svg" class="img-icon">
                                    </span>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                    <!-- ICD Ends -->
                </div>
            </ng-template>
        </mat-tab>
        <mat-tab label="History">
            <ng-template matTabContent>
                <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                    [historyTotalCount]="historyTotalCount"></app-m-view-history>
            </ng-template>
        </mat-tab>
        <mat-tab label="Note">
            <ng-template matTabContent>
                <p>Notes</p>
            </ng-template>
        </mat-tab>
        <mat-tab label="Attachments">
            <ng-template matTabContent>
                <p>Attachments</p>
            </ng-template>
        </mat-tab>
    </mat-tab-group>
</div>

<!-- Attachments Popup -->
<div *ngIf="showAttachmentsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Attachments</span>
            <button class="close-btn" (click)="closeAttachmentsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 pt-0 bg-white">
            <div class="attachment-list">
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-50px w-50px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <div class="flex items-center justify-center text-sm font-medium">
                                        PDF
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Lorem ipsum.pdf
                                    </div>
                                    <div class="text-secondary text-sm font-medium">
                                        Jhoette Dumlao
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="attachment-actions">
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-Download.svg" class="img-icon">
                            </button>
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-eye.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Elit neque amet urna nisl arcu. Vitae enim pellentesque consequat amet neque laoreet
                        posuere. Facilisis sit integer in mi in.
                    </div>
                </div>
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-50px w-50px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <div class="flex items-center justify-center text-sm font-medium">
                                        PDF
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Lorem ipsum.pdf
                                    </div>
                                    <div class="text-secondary text-sm font-medium">
                                        Jhoette Dumlao
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="attachment-actions">
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-Download.svg" class="img-icon">
                            </button>
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-eye.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Elit neque amet urna nisl arcu. Vitae enim pellentesque consequat amet neque laoreet
                        posuere. Facilisis sit integer in mi in.
                    </div>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <label class="file-upload-label">
                    <input type="file" style="display:none" />
                    <span class="file-upload-text">
                        <img src="assets/icons/icon-PlusCircle-s.svg" class="upload-icon" />
                        Click here to browse files
                    </span>
                </label>
                <input type="text" class="form-control" placeholder="Comment Optional" />
                <button class="upload-btn">Upload</button>
            </div>
        </div>

    </div>
</div>

<!-- CPT Codes popup starts -->
<div *ngIf="showCPTCodesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white px-4 py-3">
            <div class="flex flex-col">
                <span>CPT Codes</span>
                <span class="text-secondary text-sm">Optional description</span>
            </div>
            <button class="close-btn" (click)="closeCPTCodesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 p-0 bg-white">
            <app-m-cptdata [lisfOfCPTData]="lisfOfCPTData"></app-m-cptdata>
        </div>
    </div>
</div>
<!-- CPT Codes popup ends -->

<!-- ICD Codes popup starts -->
<div *ngIf="showICDCodesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white px-4 py-3">
            <div class="flex flex-col">
                <span>ICD Codes</span>
                <span class="text-secondary text-sm">Optional description</span>
            </div>
            <button class="close-btn" (click)="closeICDCodesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 p-0 bg-white">
            <app-m-icddata [lisfOfICDData]="lisfOfICDData"></app-m-icddata>
        </div>
    </div>
</div>
<!-- ICD Codes popup end -->

<!-- Submit Encounter Popup -->
<div *ngIf="showSubmitEncounterPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Submit Encounter</span>
            <button class="close-btn" (click)="closeSubmitEncounterPop()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 pt-0 bg-white">
            <div class="attachment-list">
                <div class="w-full">
                    <mat-select id="physician" class="form-control" [value]="''">
                        <mat-option [value]="''">Select Physician</mat-option>
                        <mat-option [value]="1">Physician 1</mat-option>
                        <mat-option [value]="2">Physician 2</mat-option>
                    </mat-select>
                </div>
                <div class="w-full">
                    <mat-form-field [subscriptSizing]="'dynamic'" class="w-full">
                        <mtx-datetimepicker #datetimePicker5 [type]="type" [mode]="mode"
                            [multiYearSelector]="multiYearSelector" [startView]="startView" [twelvehour]="twelvehour"
                            [timeInterval]="timeInterval" [touchUi]="touchUi" [timeInput]="timeInput">
                        </mtx-datetimepicker>
                        <input [mtxDatetimepicker]="datetimePicker5" [max]="maxDateTime" class="form-row"
                            [(ngModel)]="testData.encounterSeenDate" matInput required>
                        <mtx-datetimepicker-toggle [for]="datetimePicker5" matSuffix></mtx-datetimepicker-toggle>
                    </mat-form-field>
                </div>
                <div class="w-full">
                    <mat-slide-toggle [(ngModel)]="isMultipleEncounter">
                        Edit
                    </mat-slide-toggle>
                    <!-- <mat-slide-toggle (change)="toggleCompleted($event)">
                        Edit
                    </mat-slide-toggle> -->
                </div>
                <div class="w-full">
                    <mat-form-field [subscriptSizing]="'dynamic'" class="w-full">
                        <ngx-multiple-dates [matDatepicker]="picker" name="excludedDates"
                            [(ngModel)]="listOfEncounterSeenDates" [min]="minDate" [max]="maxDate"
                            placeholder="Select dates">
                        </ngx-multiple-dates>
                        <mat-datepicker-toggle matPrefix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <button class="upload-btn">Submit</button>
            </div>
        </div>

    </div>
</div>

<!-- Notes Popup -->
<div *ngIf="showNotesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Notes</span>
            <button class="close-btn" (click)="closeNotesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 pt-0 bg-white">
            <div class="attachment-list">
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-40px w-40px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <img src="assets/icons/icon-24-white.svg" class="img-icon">
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Aparna Kalluru
                                    </div>
                                    <div class="text-secondary text-sm">
                                        Aug 12, 2025 04:45 PM
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Test comments here
                    </div>
                </div>
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-40px w-40px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <img src="assets/icons/icon-24-white.svg" class="img-icon">
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Aparna Kalluru
                                    </div>
                                    <div class="text-secondary text-sm">
                                        Aug 12, 2025 04:45 PM
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Test comments here
                    </div>
                </div>
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-40px w-40px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <img src="assets/icons/icon-24-white.svg" class="img-icon">
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Aparna Kalluru
                                    </div>
                                    <div class="text-secondary text-sm">
                                        Aug 12, 2025 04:45 PM
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Test comments here
                    </div>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <div class="facility-dropdown mr-0">
                    <mat-label>Users and Groups</mat-label>
                    <mat-select id="subgroup" class="form-control" [value]="1">
                        <mat-option [value]="1">User 1</mat-option>
                        <mat-option [value]="2">User 2</mat-option>
                        <mat-option [value]="3">User 3</mat-option>
                    </mat-select>
                </div>
                <input type="text" class="form-control" placeholder="Comment" />
                <button class="upload-btn">Send</button>
            </div>
        </div>
    </div>
</div>

<!-- Patient History Popup -->
<div *ngIf="showEncountersPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideUp]>
        <div class="actions-header">
            <span>Past Encounters</span>
            <button class="close-btn" (click)="closePatientHistoryPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70">
            <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                [historyTotalCount]="historyTotalCount"></app-m-view-history>
        </div>
    </div>
</div>

<!-- Encounter details Popup -->
<!-- <div *ngIf="showEncounterDetailsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header">
            <button class="close-btn" (click)="closeEncounterDetailsPopup()">
                <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
            </button>
            <button class="close-btn" (click)="closeEncounterDetailsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70 pt-0">
            <div class="patient-cards">
                <div class="patient-card flex flex-col">
                    <div class="p-3 border-b">
                        <span class="font-medium">Details</span>
                    </div>
                    <div class="flex flex-col p-3">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <td>Account</td>
                                    <td><span class="font-medium">************</span></td>
                                </tr>
                                <tr>
                                    <td>Room</td>
                                    <td><span class="font-medium">0220T-B</span></td>
                                </tr>
                                <tr>
                                    <td>Type</td>
                                    <td><span class="font-medium">Inpatient</span></td>
                                </tr>
                                <tr>
                                    <td>DOB</td>
                                    <td><span class="font-medium">11/17/1985</span></td>
                                </tr>
                                <tr>
                                    <td>LOS</td>
                                    <td><span class="font-medium">LOS:2 GLOS:4</span></td>
                                </tr>
                                <tr>
                                    <td>Attending</td>
                                    <td><span class="font-medium">Lion Cantaloupe, Cheetah Apple</span></td>
                                </tr>
                                <tr>
                                    <td>Payor</td>
                                    <td><span class="font-medium">Humana Gold Care</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="p-3 border-b">
                        <span class="font-medium">CPT Codes</span>
                    </div>
                    <div class="flex flex-col p-3">
                        <div class="p-2 border-b">99396 - Preventive medicine visit, established patient, ages 40-64.
                        </div>
                        <div class="p-2">28170 - Arthrodesis, great toe.</div>
                    </div>
                </div>
                <div class="patient-card flex flex-col">
                    <div class="p-3 border-b">
                        <span class="font-medium">ICD Codes</span>
                    </div>
                    <div class="flex flex-col p-3">
                        <div class="p-2 border-b">99396 - Preventive medicine visit, established patient, ages 40-64.
                        </div>
                        <div class="p-2">28170 - Arthrodesis, great toe.</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div> -->