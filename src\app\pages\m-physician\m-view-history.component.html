<div class="patient-cards mobile-p-3">
    <!-- <div class="patient-card flex flex-col" *ngFor="let item of listOfPatientHistory">
        <div class="text-center bg-gray-200 p-2">
            {{item.encounterseendate}}
        </div>
        <div class="flex flex-col mobile-p-3">
            <div class="flex">
                <span class="mobile-text-lg font-medium text-break">{{patient.patient_Name}}</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{patient.dbo}} ({{patient.age}}{{patient.sex}})</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{patient.facility_Name}}</span>
            </div>
            <div class="my-2 h-1 w-12 border-t-2"></div>
            <div class="grid w-full grid-cols-2 gap-x-4">
                <div>{{patient.account_Number || '-'}}</div>
                <div>{{patient.room_Number || '-'}}</div>
                <div>{{patient.admission_Type || '-'}}</div>
                <div class="flex items-center">
                    <span>{{patient.arithmetic_Mean_LOS || '-'}}</span>
                </div>
            </div>
            <div class="mt-2 flex items-center">
                <div>
                    <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                </div>
                <div class="ml-2">
                    {{item.physicianname || '-'}}
                </div>
            </div>
        </div>
    </div> -->
    <div class="patient-card flex flex-col" *ngFor="let item of listOfPatientHistory">
        <div class="text-center bg-gray-200 p-2">
            {{item.encounterseendate}}
        </div>
        <div class="flex flex-col mobile-p-3 cursor-pointer" (click)="viewEncoutnerHistory(item.encounter_id)">
            <div class="flex">
                <span class="mobile-text-lg font-medium">{{patient.patient_Name}}</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{patient.dbo}} ({{patient.age}}{{patient.sex}})</span>
                <div class="mx-2 h-6 border-l-2"></div>
                <span class="font-normal">{{patient.facility_Name}}</span>
            </div>
            <div class="my-2 h-1 w-12 border-t-2"></div>
            <div class="grid w-full grid-cols-2 gap-x-4">
                <div>{{patient.account_Number || '-'}}</div>
                <div>{{patient.room_Number || '-'}}</div>
                <div>{{patient.admission_Type || '-'}}</div>
                <div class="flex items-center">
                    <span>{{patient.arithmetic_Mean_LOS || '-'}}</span>
                    <!-- <div class="mx-2 h-4 border-l-2"></div>
                                <span>GLOS:4</span> -->
                </div>
            </div>
            <div class="mt-2 flex items-center">
                <div>
                    <img src="assets/icons/icon-hc-Stethoscope-Doctor.svg" class="img-icon">
                </div>
                <div class="ml-2">
                    {{item.physicianname || '-'}}
                </div>
            </div>
        </div>
    </div>
    <div class="patient-card flex flex-col" *ngIf="listOfPatientHistory?.length == 0">
        <div class="flex flex-col p-6 items-center justify-center h-20">
            No data
        </div>
    </div>
</div>


<!-- Encounter details Popup -->
<div *ngIf="showEncounterDetailsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header">
            <button class="close-btn" (click)="closeEncounterDetailsPopup()">
                <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
            </button>
            <button class="close-btn" (click)="closeEncounterDetailsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70 pt-0">
            <app-m-view-encounter-history [listOfHistory]="listOfHistory"></app-m-view-encounter-history>
        </div>

    </div>
</div>